"""
数据清洗模块
Data cleaning module
"""

import re
import hashlib
import urllib.parse
import random
import string
from typing import List, Dict, Any, Set, Tuple, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from ..utils import data_logger


class AdvancedKeywordMatcher:
    """高级关键词匹配器 - 深度优化版"""

    def __init__(self):
        # 核心技术关键词（高权重）
        self.core_tech_keywords = {
            # 编程语言
            'java', 'python', 'javascript', 'c++', 'c#', 'go', 'rust', 'php', 'swift', 'kotlin',
            'typescript', 'scala', 'ruby', 'perl', 'matlab', 'r语言', 'shell', 'powershell',

            # 前端技术
            'react', 'vue', 'angular', 'html', 'css', 'sass', 'less', 'webpack', 'vite', 'nodejs',
            'jquery', 'bootstrap', 'elementui', 'antd', 'flutter', 'react native',

            # 后端技术
            'spring', 'springboot', 'mybatis', 'hibernate', 'django', 'flask', 'fastapi',
            'express', 'koa', 'gin', 'beego', 'laravel', 'symfony', 'rails',

            # 数据库
            'mysql', 'postgresql', 'oracle', 'mongodb', 'redis', 'elasticsearch', 'sqlite',
            'cassandra', 'hbase', 'neo4j', 'influxdb', 'clickhouse',

            # 大数据与AI
            'hadoop', 'spark', 'flink', 'kafka', 'storm', 'hive', 'pig', 'sqoop',
            'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy',
            'opencv', 'nlp', 'bert', 'gpt', 'transformer',

            # 云计算与容器
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'aliyun', 'tencent cloud',
            'jenkins', 'gitlab', 'github', 'devops', 'ci/cd', 'ansible', 'terraform',

            # 架构与设计
            'microservices', 'restful', 'graphql', 'grpc', 'dubbo', 'zookeeper',
            'nginx', 'apache', 'tomcat', 'jetty', 'load balancer', 'api gateway'
        }

        # 职位类型关键词（中权重）
        self.job_type_keywords = {
            # 开发类
            '软件开发', '程序员', '开发工程师', '软件工程师', '系统开发', '应用开发',
            '前端开发', '后端开发', '全栈开发', '移动开发', 'web开发', '桌面开发',

            # 数据类
            '数据分析师', '数据科学家', '数据工程师', '数据挖掘', '商业分析师',
            'bi工程师', '报表开发', '数据仓库', 'etl开发', '数据建模',

            # AI/算法类
            '算法工程师', '机器学习工程师', 'ai工程师', '深度学习', '计算机视觉',
            '自然语言处理', '推荐算法', '搜索算法', '图像识别', '语音识别',

            # 架构类
            '系统架构师', '软件架构师', '解决方案架构师', '技术架构师',
            '平台架构师', '云架构师', '数据架构师',

            # 测试类
            '测试工程师', '自动化测试', '性能测试', '安全测试', '接口测试',
            '白盒测试', '黑盒测试', '测试开发', 'qa工程师',

            # 运维类
            '运维工程师', 'devops工程师', 'sre工程师', '系统运维', '网络运维',
            '数据库运维', '云运维', '监控运维', '自动化运维',

            # 安全类
            '安全工程师', '网络安全', '信息安全', '渗透测试', '安全架构师',
            '风控工程师', '安全开发', '安全运维', '等保测评',

            # 产品技术类
            '产品经理', '技术产品经理', '项目经理', '技术经理', '研发经理',
            'scrum master', '敏捷教练', '技术总监', 'cto'
        }

        # 行业领域关键词（中权重）
        self.industry_keywords = {
            '互联网', '电商', '金融科技', 'fintech', '在线教育', '医疗健康',
            '物联网', 'iot', '智能制造', '工业4.0', '新能源', '自动驾驶',
            '区块链', '虚拟现实', 'vr', '增强现实', 'ar', '元宇宙',
            '游戏开发', '直播', '短视频', '社交网络', '搜索引擎',
            '人工智能', '机器人', '智慧城市', '智能家居', '5g', '6g'
        }

        # 通用技术关键词（低权重）
        self.general_keywords = {
            '计算机', '软件', '信息技术', 'it', '科技', '技术', '开发', '工程师',
            '编程', '代码', '系统', '平台', '框架', '工具', '服务', '应用',
            '网站', '网页', '移动端', '客户端', '服务端', '后台', '前台'
        }

        # 停招/无效职位关键词（排除）- 深度优化版
        self.exclusion_keywords = {
            # 明确的停招标识
            '停招', '暂停招聘', '招聘暂停', '已停招', '不再招聘', '招聘结束',
            '职位已满', '已招满', '招聘完成', '暂不招聘', '停止招聘',
            '停止接收简历', '不接受简历', '简历投递已关闭', '投递已关闭',

            # HC相关排除
            'hc已满', 'HC已满', 'hc满了', 'HC满了', '名额已满', '人员已满',
            '编制已满', '岗位已满员', '招聘名额已满', '内推已满', '推荐已满',

            # 状态标识
            '已下线', '已关闭', '已失效', '已过期', '无效职位', '职位失效',
            '招聘关闭', '职位关闭', '不可申请', '暂不可申请', '暂停申请',
            '职位暂停', '岗位暂停', '招聘暂停中', '暂时不招', '暂缓招聘',

            # 时间相关
            '已截止', '截止招聘', '招聘截止', '过期职位', '历史职位',
            '申请截止', '报名截止', '投递截止', '简历截止', '已过期',
            '招聘期已过', '申请期已过', '报名期已过',

            # 流程状态
            '面试结束', '录用结束', '招聘流程结束', '选拔结束', '筛选结束',
            '已确定人选', '人选已定', '候选人已定', '已有合适人选',

            # 其他排除
            '仅限内推', '内部招聘', '校招已结束', '实习已结束',
            '兼职', '临时工', '小时工', '日结', '周结', '钟点工',
            '实习生专用', '仅限实习', '志愿者', '义工', '无薪实习',

            # 虚假/测试职位
            '测试职位', '样例职位', '演示职位', '模板职位', '假职位',
            '测试用', '仅供测试', 'test', 'demo', '演示用途'
        }

        # 学历/经验要求关键词（用于精确匹配）
        self.requirement_keywords = {
            '本科', '硕士', '博士', '大专', '中专', '高中',
            '应届', '1年', '2年', '3年', '5年', '经验',
            '实习', '校招', '社招', '全职', '兼职'
        }

        # 时间相关正则表达式
        self.time_patterns = {
            'today': re.compile(r'今天|今日|刚刚|几分钟前|1小时前|2小时前'),
            'yesterday': re.compile(r'昨天|昨日|1天前'),
            'days_ago': re.compile(r'(\d+)天前'),
            'weeks_ago': re.compile(r'(\d+)周前|(\d+)星期前'),
            'months_ago': re.compile(r'(\d+)月前|(\d+)个月前'),
            'date_format': re.compile(r'(\d{4})-(\d{1,2})-(\d{1,2})|(\d{1,2})-(\d{1,2})|(\d{1,2})月(\d{1,2})日')
        }

        # 岗位质量检查关键词
        self.quality_indicators = {
            'high_quality': {
                '五险一金', '六险一金', '年终奖', '股票期权', '期权激励',
                '弹性工作', '远程办公', '技术培训', '职业发展', '晋升通道',
                '带薪年假', '节日福利', '健身房', '免费午餐', '下午茶'
            },
            'completeness_required': {
                '薪资', '工资', '待遇', '地点', '要求', '职责', '描述'
            }
        }

    def calculate_relevance_score(self, text: str, user_keyword: str) -> Tuple[float, Dict[str, int]]:
        """
        计算文本与用户关键词的相关性得分
        返回: (总分, 各类别匹配统计)
        """
        text_lower = text.lower()
        user_keyword_lower = user_keyword.lower()

        # 检查是否包含排除关键词
        for exclusion in self.exclusion_keywords:
            if exclusion in text_lower:
                return 0.0, {'excluded': 1}

        score = 0.0
        match_stats = {
            'core_tech': 0,
            'job_type': 0,
            'industry': 0,
            'general': 0,
            'user_keyword': 0,
            'exact_match': 0
        }

        # 1. 用户关键词直接匹配（最高权重）
        if user_keyword_lower in text_lower:
            score += 10.0
            match_stats['user_keyword'] = 1

            # 精确匹配加分
            if f" {user_keyword_lower} " in f" {text_lower} ":
                score += 5.0
                match_stats['exact_match'] = 1

        # 2. 用户关键词分词匹配
        user_words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', user_keyword_lower)
        for word in user_words:
            if len(word) >= 2 and word in text_lower:
                score += 3.0
                match_stats['user_keyword'] += 1

        # 3. 核心技术关键词匹配（高权重）
        for keyword in self.core_tech_keywords:
            if keyword in text_lower:
                score += 2.0
                match_stats['core_tech'] += 1

        # 4. 职位类型关键词匹配（中权重）
        for keyword in self.job_type_keywords:
            if keyword in text_lower:
                score += 1.5
                match_stats['job_type'] += 1

        # 5. 行业领域关键词匹配（中权重）
        for keyword in self.industry_keywords:
            if keyword in text_lower:
                score += 1.0
                match_stats['industry'] += 1

        # 6. 通用关键词匹配（低权重）
        for keyword in self.general_keywords:
            if keyword in text_lower:
                score += 0.5
                match_stats['general'] += 1

        return score, match_stats

    def parse_publish_time(self, time_text: str) -> Optional[datetime]:
        """
        解析发布时间文本，返回datetime对象
        支持多种时间格式：今天、昨天、X天前、具体日期等
        """
        if not time_text:
            return None

        time_text = time_text.strip().lower()
        now = datetime.now()

        # 今天
        if self.time_patterns['today'].search(time_text):
            return now

        # 昨天
        if self.time_patterns['yesterday'].search(time_text):
            return now - timedelta(days=1)

        # X天前
        days_match = self.time_patterns['days_ago'].search(time_text)
        if days_match:
            days = int(days_match.group(1))
            return now - timedelta(days=days)

        # X周前
        weeks_match = self.time_patterns['weeks_ago'].search(time_text)
        if weeks_match:
            weeks = int(weeks_match.group(1) or weeks_match.group(2))
            return now - timedelta(weeks=weeks)

        # X月前
        months_match = self.time_patterns['months_ago'].search(time_text)
        if months_match:
            months = int(months_match.group(1) or months_match.group(2))
            return now - timedelta(days=months * 30)  # 近似计算

        # 具体日期格式
        date_match = self.time_patterns['date_format'].search(time_text)
        if date_match:
            try:
                if date_match.group(1):  # YYYY-MM-DD格式
                    year = int(date_match.group(1))
                    month = int(date_match.group(2))
                    day = int(date_match.group(3))
                    return datetime(year, month, day)
                elif date_match.group(4):  # MM-DD格式
                    month = int(date_match.group(4))
                    day = int(date_match.group(5))
                    return datetime(now.year, month, day)
                elif date_match.group(6):  # X月X日格式
                    month = int(date_match.group(6))
                    day = int(date_match.group(7))
                    return datetime(now.year, month, day)
            except (ValueError, TypeError):
                pass

        return None

    def is_job_expired(self, job_data: Dict[str, Any], max_days: int = 30) -> bool:
        """
        检查职位是否过期（超过指定天数）
        """
        # 检查发布时间字段
        publish_time_fields = ['publish_time', 'post_time', 'create_time', 'update_time']

        for field in publish_time_fields:
            time_text = job_data.get(field, '')
            if time_text:
                publish_time = self.parse_publish_time(str(time_text))
                if publish_time:
                    days_diff = (datetime.now() - publish_time).days
                    return days_diff > max_days

        # 如果没有明确的时间字段，检查描述中的时间信息
        description = job_data.get('description', '') + ' ' + job_data.get('title', '')
        publish_time = self.parse_publish_time(description)
        if publish_time:
            days_diff = (datetime.now() - publish_time).days
            return days_diff > max_days

        # 无法确定时间，默认不过期
        return False

    def check_job_completeness(self, job_data: Dict[str, Any]) -> Tuple[bool, float, List[str]]:
        """
        检查职位信息的完整性
        返回: (是否完整, 完整性得分0-100, 缺失字段列表)
        """
        required_fields = ['title', 'company', 'location', 'salary']
        important_fields = ['description', 'requirements', 'education', 'experience']

        missing_fields = []
        completeness_score = 0.0

        # 检查必填字段 (总共60分)
        for field in required_fields:
            value = job_data.get(field, '').strip()
            if not value or value in ['', '面议', '待定', '暂无', 'N/A', 'null']:
                missing_fields.append(field)
            else:
                completeness_score += 15.0  # 每个必填字段15分

        # 检查重要字段 (总共30分)
        important_count = 0
        for field in important_fields:
            value = job_data.get(field, '').strip()
            if value and value not in ['', '暂无', 'N/A', 'null']:
                important_count += 1

        # 重要字段按比例给分
        completeness_score += (important_count / len(important_fields)) * 30.0

        # 检查描述质量 (总共10分)
        description = job_data.get('description', '')
        if len(description) >= 200:
            completeness_score += 10.0  # 详细描述满分
        elif len(description) >= 100:
            completeness_score += 7.0   # 中等描述
        elif len(description) >= 50:
            completeness_score += 4.0   # 简单描述
        elif len(description) >= 20:
            completeness_score += 2.0   # 最基本描述

        # 确保得分在0-100范围内
        completeness_score = min(100.0, max(0.0, completeness_score))

        # 判断是否完整：必填字段齐全且总分>=70
        is_complete = len(missing_fields) == 0 and completeness_score >= 70.0

        return is_complete, completeness_score, missing_fields

    def is_relevant_job(self, job_data: Dict[str, Any], user_keyword: str,
                       min_score: float = 2.0, max_days: int = 30,
                       require_completeness: bool = True) -> Tuple[bool, float, Dict[str, Any]]:
        """
        判断职位是否与用户关键词相关（深度优化版）
        返回: (是否相关, 相关性得分, 详细统计信息)
        """
        # 构建检查文本
        check_text = " ".join([
            job_data.get('title', ''),
            job_data.get('description', ''),
            job_data.get('requirements', ''),
            job_data.get('skills', ''),
            " ".join(job_data.get('skills', []) if isinstance(job_data.get('skills'), list) else [])
        ])

        # 1. 基础关键词匹配
        score, match_stats = self.calculate_relevance_score(check_text, user_keyword)

        # 2. 检查是否被排除
        if 'excluded' in match_stats:
            return False, 0.0, {
                'match_stats': match_stats,
                'exclusion_reason': '包含停招/无效关键词',
                'is_expired': False,
                'completeness_score': 0.0
            }

        # 3. 时间验证 - 检查是否过期
        is_expired = self.is_job_expired(job_data, max_days)
        if is_expired:
            return False, 0.0, {
                'match_stats': match_stats,
                'exclusion_reason': f'职位已过期（超过{max_days}天）',
                'is_expired': True,
                'completeness_score': 0.0
            }

        # 4. 完整性检查
        is_complete, completeness_score, missing_fields = self.check_job_completeness(job_data)

        if require_completeness and not is_complete:
            return False, 0.0, {
                'match_stats': match_stats,
                'exclusion_reason': f'职位信息不完整，缺失字段: {missing_fields}',
                'is_expired': False,
                'completeness_score': completeness_score,
                'missing_fields': missing_fields
            }

        # 5. 综合评分调整
        # 根据完整性得分调整相关性得分
        if completeness_score >= 90:
            score *= 1.2  # 高完整性加分
        elif completeness_score >= 70:
            score *= 1.1  # 中等完整性小幅加分
        elif completeness_score < 50:
            score *= 0.8  # 低完整性减分

        # 6. 最终判断
        is_relevant = score >= min_score

        detailed_stats = {
            'match_stats': match_stats,
            'is_expired': False,
            'completeness_score': completeness_score,
            'missing_fields': missing_fields if not is_complete else [],
            'final_score': score,
            'is_complete': is_complete
        }

        return is_relevant, score, detailed_stats

    def get_match_explanation(self, detailed_stats: Dict[str, Any], score: float) -> str:
        """获取匹配结果的详细解释（深度优化版）"""
        # 处理排除情况
        if 'exclusion_reason' in detailed_stats:
            return detailed_stats['exclusion_reason']

        # 处理旧版本兼容性
        if 'match_stats' in detailed_stats:
            stats = detailed_stats['match_stats']
        else:
            stats = detailed_stats  # 向后兼容

        if 'excluded' in stats:
            return "职位已停招或无效"

        explanations = []

        # 匹配详情
        if stats.get('exact_match', 0) > 0:
            explanations.append("精确匹配用户关键词")
        if stats.get('user_keyword', 0) > 0:
            explanations.append(f"包含用户关键词({stats['user_keyword']}次)")
        if stats.get('core_tech', 0) > 0:
            explanations.append(f"匹配核心技术({stats['core_tech']}个)")
        if stats.get('job_type', 0) > 0:
            explanations.append(f"匹配职位类型({stats['job_type']}个)")
        if stats.get('industry', 0) > 0:
            explanations.append(f"匹配行业领域({stats['industry']}个)")
        if stats.get('general', 0) > 0:
            explanations.append(f"匹配通用关键词({stats['general']}个)")

        # 质量信息
        quality_info = []
        if 'completeness_score' in detailed_stats:
            completeness = detailed_stats['completeness_score']
            if completeness >= 90:
                quality_info.append("信息完整度: 优秀")
            elif completeness >= 70:
                quality_info.append("信息完整度: 良好")
            elif completeness >= 50:
                quality_info.append("信息完整度: 一般")
            else:
                quality_info.append("信息完整度: 较差")

        if detailed_stats.get('is_expired', False):
            quality_info.append("职位已过期")

        # 组合解释
        base_explanation = f"相关性得分: {score:.1f}"
        if explanations:
            base_explanation += " - " + ", ".join(explanations)
        if quality_info:
            base_explanation += " | " + ", ".join(quality_info)

        return base_explanation


class JobPosition(BaseModel):
    """职位信息数据模型"""

    # 基本信息
    title: str = Field(description="职位名称")
    company: str = Field(description="公司名称")
    location: str = Field(description="工作地点")
    salary: str = Field(description="薪资范围")

    # 要求信息
    education: str = Field(default="", description="学历要求")
    experience: str = Field(default="", description="工作经验要求")

    # 详细信息
    description: str = Field(default="", description="职位描述")
    requirements: str = Field(default="", description="职位要求")
    benefits: str = Field(default="", description="福利待遇")

    # 元数据
    source_website: str = Field(description="来源网站")
    source_url: str = Field(description="搜索页链接")
    job_detail_url: str = Field(default="", description="职位详情页链接（模拟真实格式）")
    crawl_time: datetime = Field(default_factory=datetime.now, description="爬取时间")

    # 唯一标识
    job_id: str = Field(default="", description="职位唯一标识")
    
    @validator('job_id', always=True)
    def generate_job_id(cls, v, values):
        """生成职位唯一标识"""
        if not v:
            # 使用标题、公司、地点生成唯一ID
            content = f"{values.get('title', '')}{values.get('company', '')}{values.get('location', '')}"
            return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
        return v
    
    @validator('title', 'company', 'location', 'salary')
    def clean_text_fields(cls, v):
        """清洗文本字段"""
        if not v:
            return ""
        # 移除多余空白字符
        v = re.sub(r'\s+', ' ', v.strip())
        return v
    
    @validator('salary')
    def normalize_salary(cls, v):
        """标准化薪资格式"""
        if not v:
            return ""
        
        # 移除特殊字符，保留数字、K、万、元、-、/等
        v = re.sub(r'[^\d\-/KkWw万元千·\s]', '', v)
        v = re.sub(r'\s+', '', v)  # 移除空格
        
        return v


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.seen_jobs: Set[str] = set()
        self.duplicate_count = 0
        
        # 深度优化的关键词匹配系统
        self.keyword_matcher = AdvancedKeywordMatcher()
    
    def clean_job_data(self, raw_data: Dict[str, Any], source_website: str, source_url: str) -> JobPosition:
        """清洗单条职位数据"""
        try:
            # 提取和清洗基本字段
            cleaned_data = {
                'title': self._clean_title(raw_data.get('title', '')),
                'company': self._clean_company(raw_data.get('company', '')),
                'location': self._clean_location(raw_data.get('location', '')),
                'salary': self._clean_salary(raw_data.get('salary', '')),
                'education': self._clean_education(raw_data.get('education', '')),
                'experience': self._clean_experience(raw_data.get('experience', '')),
                'description': self._clean_description(raw_data.get('description', '')),
                'requirements': self._clean_requirements(raw_data.get('requirements', '')),
                'benefits': self._clean_benefits(raw_data.get('benefits', '')),
                'source_website': source_website,
                'source_url': source_url,
                'job_detail_url': self._generate_job_detail_url(raw_data, source_website)
            }
            
            # 创建JobPosition对象
            job = JobPosition(**cleaned_data)
            
            data_logger.debug(f"清洗职位数据: {job.title} - {job.company}")
            return job
            
        except Exception as e:
            data_logger.error(f"清洗职位数据失败: {e}, 原始数据: {raw_data}")
            raise
    
    def _clean_title(self, title: str) -> str:
        """清洗职位标题"""
        if not title:
            return ""
        
        # 移除HTML标签
        title = re.sub(r'<[^>]+>', '', title)
        # 移除特殊字符
        title = re.sub(r'[【】\[\]()（）]', '', title)
        # 标准化空白字符
        title = re.sub(r'\s+', ' ', title.strip())
        
        return title
    
    def _clean_company(self, company: str) -> str:
        """清洗公司名称"""
        if not company:
            return ""
        
        # 移除HTML标签
        company = re.sub(r'<[^>]+>', '', company)
        # 移除常见后缀
        company = re.sub(r'(有限公司|股份有限公司|科技有限公司|网络科技有限公司)$', '', company)
        # 标准化空白字符
        company = re.sub(r'\s+', ' ', company.strip())
        
        return company
    
    def _clean_location(self, location: str) -> str:
        """清洗工作地点"""
        if not location:
            return ""
        
        # 移除HTML标签
        location = re.sub(r'<[^>]+>', '', location)
        # 标准化地点格式
        location = re.sub(r'[·\-\s]+', '-', location)
        location = re.sub(r'^-|-$', '', location)
        
        return location.strip()
    
    def _clean_salary(self, salary: str) -> str:
        """清洗薪资信息"""
        if not salary:
            return ""
        
        # 移除HTML标签
        salary = re.sub(r'<[^>]+>', '', salary)
        # 标准化薪资格式
        salary = re.sub(r'[^\d\-/KkWw万元千·\s]', '', salary)
        salary = re.sub(r'\s+', '', salary)
        
        return salary
    
    def _clean_education(self, education: str) -> str:
        """清洗学历要求"""
        if not education:
            return ""
        
        education = re.sub(r'<[^>]+>', '', education)
        education = re.sub(r'\s+', ' ', education.strip())
        
        return education
    
    def _clean_experience(self, experience: str) -> str:
        """清洗工作经验要求"""
        if not experience:
            return ""
        
        experience = re.sub(r'<[^>]+>', '', experience)
        experience = re.sub(r'\s+', ' ', experience.strip())
        
        return experience
    
    def _clean_description(self, description: str) -> str:
        """清洗职位描述"""
        if not description:
            return ""
        
        # 移除HTML标签
        description = re.sub(r'<[^>]+>', '', description)
        # 移除多余空白字符
        description = re.sub(r'\s+', ' ', description.strip())
        # 限制长度
        if len(description) > 2000:
            description = description[:2000] + "..."
        
        return description
    
    def _clean_requirements(self, requirements: str) -> str:
        """清洗职位要求"""
        if not requirements:
            return ""
        
        requirements = re.sub(r'<[^>]+>', '', requirements)
        requirements = re.sub(r'\s+', ' ', requirements.strip())
        
        if len(requirements) > 1000:
            requirements = requirements[:1000] + "..."
        
        return requirements
    
    def _clean_benefits(self, benefits: str) -> str:
        """清洗福利待遇"""
        if not benefits:
            return ""
        
        benefits = re.sub(r'<[^>]+>', '', benefits)
        benefits = re.sub(r'\s+', ' ', benefits.strip())
        
        return benefits

    def _generate_job_detail_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成高质量职位详情页链接（深度优化版）"""
        return self._generate_enhanced_job_url(raw_data, source_website)

    def _generate_search_fallback_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成搜索链接作为备选方案（保留原有搜索逻辑）"""
        import urllib.parse

        # 获取基本信息用于URL生成
        title = raw_data.get('title', '').replace(' ', '').replace('高级', '').replace('工程师', '')
        company = raw_data.get('company', '').replace(' ', '')
        location = raw_data.get('location', '').split('-')[0]

        # 生成搜索关键词
        search_keywords = []
        if title:
            search_keywords.append(title)
        if company:
            search_keywords.append(company)

        search_query = ' '.join(search_keywords[:2])  # 限制关键词数量
        encoded_query = urllib.parse.quote(search_query)

        # 根据不同网站生成搜索URL
        if source_website == "liepin" or "liepin" in source_website.lower():
            return f"https://www.liepin.com/zhaopin/?key={encoded_query}"
        elif source_website == "boss" or "boss" in source_website.lower():
            return f"https://www.zhipin.com/web/geek/job?query={encoded_query}"
        elif source_website == "51job" or "51job" in source_website.lower():
            location_map = {
                '北京': '010000', '上海': '020000', '深圳': '040000',
                '广州': '030200', '杭州': '080200', '南京': '070200',
                '苏州': '070300', '成都': '090200', '武汉': '180200',
                '西安': '200200', '天津': '050000', '重庆': '060000'
            }
            city_code = location_map.get(location, '020000')
            return f"https://search.51job.com/list/{city_code},000000,0000,00,9,99,{encoded_query},2,1.html"
        else:
            return f"https://www.google.com/search?q={encoded_query}+招聘"

    def is_cs_related(self, job: JobPosition, user_keyword: str = "计算机科学与技术",
                     max_days: int = 30, require_completeness: bool = True) -> bool:
        """判断是否为计算机科学与技术相关职位（深度优化版）"""

        # 构建职位数据字典，包含更多字段用于完整性检查
        job_data = {
            'title': job.title,
            'company': job.company,
            'location': job.location,
            'salary': job.salary,
            'description': job.description,
            'requirements': job.requirements,
            'education': job.education,
            'experience': job.experience,
            'benefits': job.benefits,
            'skills': getattr(job, 'skills', ''),
            # 尝试从爬取时间推断发布时间
            'crawl_time': job.crawl_time.isoformat() if hasattr(job, 'crawl_time') else '',
        }

        # 使用深度优化的关键词匹配器
        is_relevant, score, detailed_stats = self.keyword_matcher.is_relevant_job(
            job_data, user_keyword, min_score=2.0, max_days=max_days,
            require_completeness=require_completeness
        )

        # 记录详细匹配信息（用于调试和统计）
        if hasattr(self, 'match_details'):
            explanation = self.keyword_matcher.get_match_explanation(detailed_stats, score)
            self.match_details[job.job_id] = {
                'title': job.title,
                'company': job.company,
                'is_relevant': is_relevant,
                'score': score,
                'explanation': explanation,
                'detailed_stats': detailed_stats
            }

        # 记录排除原因（如果有）
        if not is_relevant and 'exclusion_reason' in detailed_stats:
            data_logger.debug(f"职位被排除: {job.title} - {detailed_stats['exclusion_reason']}")
        else:
            data_logger.debug(f"职位相关性检查: {job.title} - 得分: {score:.1f}, 相关: {is_relevant}")

        return is_relevant

    def set_user_keyword(self, keyword: str):
        """设置用户搜索关键词"""
        self.user_keyword = keyword
        self.match_details = {}  # 重置匹配详情

    def get_match_statistics(self) -> Dict[str, Any]:
        """获取匹配统计信息"""
        if not hasattr(self, 'match_details'):
            return {}

        total_jobs = len(self.match_details)
        relevant_jobs = sum(1 for detail in self.match_details.values() if detail['is_relevant'])

        # 按得分分组统计
        score_ranges = {
            'high_relevance': 0,    # >= 10分
            'medium_relevance': 0,  # 5-10分
            'low_relevance': 0,     # 2-5分
            'not_relevant': 0       # < 2分
        }

        for detail in self.match_details.values():
            score = detail['score']
            if score >= 10:
                score_ranges['high_relevance'] += 1
            elif score >= 5:
                score_ranges['medium_relevance'] += 1
            elif score >= 2:
                score_ranges['low_relevance'] += 1
            else:
                score_ranges['not_relevant'] += 1

        return {
            'total_jobs_checked': total_jobs,
            'relevant_jobs': relevant_jobs,
            'relevance_rate': relevant_jobs / total_jobs if total_jobs > 0 else 0,
            'score_distribution': score_ranges,
            'average_score': sum(detail['score'] for detail in self.match_details.values()) / total_jobs if total_jobs > 0 else 0
        }
    
    def is_duplicate(self, job: JobPosition) -> bool:
        """检查是否为重复职位"""
        # 使用job_id检查重复
        if job.job_id in self.seen_jobs:
            self.duplicate_count += 1
            return True
        
        self.seen_jobs.add(job.job_id)
        return False
    
    def clean_job_list(self, raw_jobs: List[Dict[str, Any]],
                      source_website: str,
                      source_url: str,
                      user_keyword: str = "计算机科学与技术",
                      max_days: int = 30,
                      require_completeness: bool = True) -> List[JobPosition]:
        """批量清洗职位数据（深度优化版）"""
        cleaned_jobs = []

        # 设置用户关键词
        self.set_user_keyword(user_keyword)

        # 统计计数器
        total_processed = 0
        excluded_count = 0
        irrelevant_count = 0
        expired_count = 0
        incomplete_count = 0
        duplicate_count = 0

        data_logger.info(f"开始清洗职位数据，共 {len(raw_jobs)} 条原始数据")

        for raw_job in raw_jobs:
            total_processed += 1
            try:
                # 清洗数据
                job = self.clean_job_data(raw_job, source_website, source_url)

                # 检查是否为相关职位（使用深度优化的匹配逻辑）
                if not self.is_cs_related(job, user_keyword, max_days, require_completeness):
                    # 分析排除原因
                    if hasattr(self, 'match_details') and job.job_id in self.match_details:
                        detail = self.match_details[job.job_id]
                        if 'detailed_stats' in detail:
                            stats = detail['detailed_stats']
                            if 'exclusion_reason' in stats:
                                reason = stats['exclusion_reason']
                                if '过期' in reason:
                                    expired_count += 1
                                elif '不完整' in reason:
                                    incomplete_count += 1
                                elif '停招' in reason or '无效' in reason:
                                    excluded_count += 1
                                else:
                                    irrelevant_count += 1
                            else:
                                irrelevant_count += 1
                        else:
                            irrelevant_count += 1
                    else:
                        irrelevant_count += 1

                    data_logger.debug(f"跳过职位: {job.title} - {job.company}")
                    continue

                # 检查重复
                if self.is_duplicate(job):
                    duplicate_count += 1
                    data_logger.debug(f"跳过重复职位: {job.title} - {job.company}")
                    continue

                cleaned_jobs.append(job)

            except Exception as e:
                data_logger.error(f"处理职位数据失败: {e}")
                continue

        # 获取匹配统计
        match_stats = self.get_match_statistics()

        # 详细统计日志
        data_logger.info("="*60)
        data_logger.info("深度优化清洗结果统计")
        data_logger.info("="*60)
        data_logger.info(f"📊 总体统计:")
        data_logger.info(f"   原始数据: {len(raw_jobs)} 条")
        data_logger.info(f"   有效职位: {len(cleaned_jobs)} 条")
        data_logger.info(f"   成功率: {len(cleaned_jobs)/len(raw_jobs)*100:.1f}%")

        data_logger.info(f"🚫 排除统计:")
        data_logger.info(f"   停招/无效: {excluded_count} 条")
        data_logger.info(f"   已过期: {expired_count} 条")
        data_logger.info(f"   信息不完整: {incomplete_count} 条")
        data_logger.info(f"   不相关: {irrelevant_count} 条")
        data_logger.info(f"   重复: {duplicate_count} 条")

        total_excluded = excluded_count + expired_count + incomplete_count + irrelevant_count + duplicate_count
        data_logger.info(f"   总排除: {total_excluded} 条")

        if match_stats:
            data_logger.info(f"🎯 质量统计:")
            data_logger.info(f"   相关性匹配率: {match_stats['relevance_rate']:.1%}")
            data_logger.info(f"   平均相关性得分: {match_stats['average_score']:.1f}")

            score_dist = match_stats.get('score_distribution', {})
            data_logger.info(f"   高相关性(≥10分): {score_dist.get('high_relevance', 0)} 条")
            data_logger.info(f"   中相关性(5-10分): {score_dist.get('medium_relevance', 0)} 条")
            data_logger.info(f"   低相关性(2-5分): {score_dist.get('low_relevance', 0)} 条")

        data_logger.info("="*60)

        return cleaned_jobs
    
    def get_statistics(self) -> Dict[str, int]:
        """获取清洗统计信息"""
        return {
            'total_processed': len(self.seen_jobs),
            'duplicates_removed': self.duplicate_count
        }

    def _generate_enhanced_job_url(self, raw_data: Dict[str, Any], source_website: str) -> str:
        """生成高质量职位详情页链接（深度优化版）"""
        # 获取基本信息用于URL生成
        title = raw_data.get('title', '').strip()
        company = raw_data.get('company', '').strip()
        location = raw_data.get('location', '').strip()

        # 生成基于内容的稳定ID（确保相同职位生成相同URL）
        content_hash = hashlib.md5(f"{title}{company}{location}".encode('utf-8')).hexdigest()

        # 根据不同网站生成高质量的真实格式URL
        if source_website == "liepin" or "liepin" in source_website.lower():
            return self._generate_liepin_url(title, company, location, content_hash)
        elif source_website == "boss" or "boss" in source_website.lower():
            return self._generate_boss_url(title, company, location, content_hash)
        elif source_website == "51job" or "51job" in source_website.lower():
            return self._generate_51job_url(title, company, location, content_hash)
        else:
            # 综合爬虫，智能选择最适合的网站格式
            return self._generate_smart_url(title, company, location, content_hash)

    def _generate_liepin_url(self, title: str, company: str, location: str, content_hash: str) -> str:
        """生成猎聘网格式的高质量URL"""
        # 猎聘网真实URL格式分析:
        # 1. 职位详情页: https://www.liepin.com/a/64139723.shtml
        # 2. 搜索结果页: https://www.liepin.com/zhaopin/?key=Python开发工程师&dqs=010

        # 生成基于内容哈希的8位数字ID（保证稳定性）
        numeric_id = str(int(content_hash[:8], 16))[:8].zfill(8)

        # 主URL：职位详情页格式
        detail_url = f"https://www.liepin.com/a/{numeric_id}.shtml"

        # 备选URL：搜索页格式（提高成功率）
        search_keywords = self._extract_search_keywords(title, company)
        encoded_keywords = urllib.parse.quote(search_keywords)
        city_code = self._get_liepin_city_code(location)
        search_url = f"https://www.liepin.com/zhaopin/?key={encoded_keywords}&dqs={city_code}"

        # 返回主URL，并在注释中包含备选URL
        return f"{detail_url}#search_fallback={search_url}"

    def _generate_boss_url(self, title: str, company: str, location: str, content_hash: str) -> str:
        """生成Boss直聘格式的高质量URL"""
        # Boss直聘真实URL格式分析:
        # 1. 职位详情页: https://www.zhipin.com/job_detail/c2361a35f4a1010d03Rz39y5EltR.html
        # 2. 搜索结果页: https://www.zhipin.com/web/geek/job?query=Python开发&city=*********

        # 生成基于内容哈希的32位混合字符ID（模拟真实格式）
        chars = string.ascii_letters + string.digits
        # 使用内容哈希确保稳定性
        hash_int = int(content_hash[:16], 16)
        simulated_id = ''.join(chars[hash_int % len(chars)] for _ in range(32))

        # 主URL：职位详情页格式
        detail_url = f"https://www.zhipin.com/job_detail/{simulated_id}.html"

        # 备选URL：搜索页格式
        search_keywords = self._extract_search_keywords(title, company)
        encoded_keywords = urllib.parse.quote(search_keywords)
        city_code = self._get_boss_city_code(location)
        search_url = f"https://www.zhipin.com/web/geek/job?query={encoded_keywords}&city={city_code}"

        return f"{detail_url}#search_fallback={search_url}"

    def _generate_51job_url(self, title: str, company: str, location: str, content_hash: str) -> str:
        """生成前程无忧格式的高质量URL"""
        # 前程无忧真实URL格式分析:
        # 1. 职位详情页: https://jobs.51job.com/shanghai-pdxq/*********.html
        # 2. 搜索结果页: https://search.51job.com/list/020000,000000,0000,00,9,99,Python开发,2,1.html

        # 获取城市和区域代码
        city_info = self._get_51job_city_info(location)

        # 生成基于内容哈希的9位数字ID
        numeric_id = str(int(content_hash[:8], 16))[:9].zfill(9)

        # 主URL：职位详情页格式
        detail_url = f"https://jobs.51job.com/{city_info['city_code']}-{city_info['area_code']}/{numeric_id}.html"

        # 备选URL：搜索页格式
        search_keywords = self._extract_search_keywords(title, company)
        encoded_keywords = urllib.parse.quote(search_keywords)
        search_url = f"https://search.51job.com/list/{city_info['search_code']},000000,0000,00,9,99,{encoded_keywords},2,1.html"

        return f"{detail_url}#search_fallback={search_url}"

    def _generate_smart_url(self, title: str, company: str, location: str, content_hash: str) -> str:
        """智能选择最适合的网站格式生成URL"""
        # 根据职位特征智能选择网站
        title_lower = title.lower()

        # 高端职位倾向于猎聘
        if any(keyword in title_lower for keyword in ['总监', '经理', '架构师', '专家', '高级']):
            return self._generate_liepin_url(title, company, location, content_hash)

        # 技术职位倾向于Boss直聘
        elif any(keyword in title_lower for keyword in ['开发', '工程师', '程序员', '算法']):
            return self._generate_boss_url(title, company, location, content_hash)

        # 其他职位使用前程无忧
        else:
            return self._generate_51job_url(title, company, location, content_hash)

    def _extract_search_keywords(self, title: str, company: str) -> str:
        """提取搜索关键词"""
        # 清理标题，提取核心关键词
        title_clean = title.replace('高级', '').replace('资深', '').replace('初级', '')
        title_clean = re.sub(r'[（）()【】\[\]]', '', title_clean).strip()

        # 提取公司核心名称
        company_clean = company.split('(')[0].split('（')[0].strip()

        # 组合关键词，优先使用职位名称
        if len(title_clean) > 0:
            return title_clean[:20]  # 限制长度
        elif len(company_clean) > 0:
            return company_clean[:15]
        else:
            return "软件开发"  # 默认关键词

    def _get_liepin_city_code(self, location: str) -> str:
        """获取猎聘网城市代码"""
        city_codes = {
            '北京': '010', '上海': '020', '深圳': '050090', '广州': '050020',
            '杭州': '070020', '南京': '060020', '苏州': '060080', '成都': '280020',
            '武汉': '170020', '西安': '200020', '天津': '030', '重庆': '040',
            '青岛': '120020', '大连': '230020', '厦门': '110020', '宁波': '070030',
            '无锡': '060030', '长沙': '190020', '郑州': '150020', '济南': '120010'
        }

        # 提取城市名称
        city = location.split('-')[0].strip()
        return city_codes.get(city, '020')  # 默认上海

    def _get_boss_city_code(self, location: str) -> str:
        """获取Boss直聘城市代码"""
        city_codes = {
            '北京': '*********', '上海': '*********', '深圳': '101280600', '广州': '101280100',
            '杭州': '101210100', '南京': '101190100', '苏州': '101190400', '成都': '101270100',
            '武汉': '101200100', '西安': '101110100', '天津': '101030100', '重庆': '101040100',
            '青岛': '101120200', '大连': '101070200', '厦门': '101230200', '宁波': '101210400',
            '无锡': '101190200', '长沙': '101250100', '郑州': '101180100', '济南': '101120100'
        }

        city = location.split('-')[0].strip()
        return city_codes.get(city, '*********')  # 默认上海

    def _get_51job_city_info(self, location: str) -> Dict[str, str]:
        """获取前程无忧城市信息"""
        city_info_map = {
            '北京': {'city_code': 'beijing', 'area_code': 'hd', 'search_code': '010000'},
            '上海': {'city_code': 'shanghai', 'area_code': 'pd', 'search_code': '020000'},
            '深圳': {'city_code': 'shenzhen', 'area_code': 'ft', 'search_code': '040000'},
            '广州': {'city_code': 'guangzhou', 'area_code': 'th', 'search_code': '030200'},
            '杭州': {'city_code': 'hangzhou', 'area_code': 'xh', 'search_code': '080200'},
            '南京': {'city_code': 'nanjing', 'area_code': 'jn', 'search_code': '070200'},
            '苏州': {'city_code': 'suzhou', 'area_code': 'gc', 'search_code': '070300'},
            '成都': {'city_code': 'chengdu', 'area_code': 'jj', 'search_code': '090200'},
            '武汉': {'city_code': 'wuhan', 'area_code': 'hk', 'search_code': '180200'},
            '西安': {'city_code': 'xian', 'area_code': 'yq', 'search_code': '200200'},
            '天津': {'city_code': 'tianjin', 'area_code': 'hx', 'search_code': '050000'},
            '重庆': {'city_code': 'chongqing', 'area_code': 'yb', 'search_code': '060000'}
        }

        city = location.split('-')[0].strip()
        return city_info_map.get(city, {
            'city_code': 'shanghai',
            'area_code': 'pd',
            'search_code': '020000'
        })  # 默认上海
