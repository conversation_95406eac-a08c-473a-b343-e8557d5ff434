#!/usr/bin/env python3
"""
深度优化关键字匹配逻辑演示脚本
Demo script for advanced keyword matching logic
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.data_processing.data_cleaner import AdvancedKeywordMatcher, DataCleaner, JobPosition


def demo_advanced_matching():
    """演示深度优化的关键字匹配功能"""
    print("🚀 深度优化关键字匹配逻辑演示")
    print("="*60)
    
    # 创建匹配器
    matcher = AdvancedKeywordMatcher()
    cleaner = DataCleaner()
    
    # 模拟真实的招聘数据
    sample_jobs = [
        {
            'title': 'Python后端开发工程师',
            'company': '阿里巴巴',
            'location': '杭州-西湖区',
            'salary': '25K-40K',
            'description': '负责电商平台后端系统开发，参与微服务架构设计。要求熟悉Python、Django、MySQL、Redis等技术栈，有大型分布式系统经验。',
            'requirements': '本科以上学历，3年以上Python开发经验，熟悉云原生技术。',
            'education': '本科',
            'experience': '3-5年',
            'benefits': '五险一金，年终奖，股票期权，弹性工作'
        },
        {
            'title': 'Java开发工程师（已停招）',
            'company': '腾讯',
            'location': '深圳-南山区',
            'salary': '20K-35K',
            'description': '负责后端开发工作，停招状态',
            'requirements': '',
            'education': '',
            'experience': ''
        },
        {
            'title': 'React前端工程师',
            'company': '字节跳动',
            'location': '北京-朝阳区',
            'salary': '22K-38K',
            'description': 'HC已满，暂停招聘。负责前端页面开发。',
            'requirements': '熟悉React、TypeScript',
            'education': '本科',
            'experience': '2-4年'
        },
        {
            'title': '算法工程师',
            'company': '百度',
            'location': '北京-海淀区',
            'salary': '30K-50K',
            'description': '负责推荐算法优化，机器学习模型训练。要求熟悉Python、TensorFlow、PyTorch等框架。',
            'requirements': '硕士以上学历，有深度学习项目经验。',
            'education': '硕士',
            'experience': '3-5年',
            'benefits': '六险一金，年终奖'
        },
        {
            'title': '销售经理',
            'company': '某传统公司',
            'location': '上海-浦东新区',
            'salary': '8K-15K',
            'description': '负责产品销售，客户维护工作。',
            'requirements': '有销售经验，沟通能力强。',
            'education': '大专',
            'experience': '1-3年'
        },
        {
            'title': '测试职位',
            'company': '测试公司',
            'location': '测试地点',
            'salary': '面议',
            'description': '这是一个测试用的职位，仅供演示。',
            'requirements': '',
            'education': '',
            'experience': ''
        },
        {
            'title': 'DevOps工程师',
            'company': '美团',
            'location': '北京-朝阳区',
            'salary': '25K-40K',
            'description': '负责CI/CD流水线建设，容器化部署，监控告警系统维护。要求熟悉Kubernetes、Docker、Jenkins等工具。',
            'requirements': '本科以上学历，熟悉云原生技术栈。',
            'education': '本科',
            'experience': '3-5年',
            'benefits': '五险一金，带薪年假'
        }
    ]
    
    print(f"📊 原始数据统计:")
    print(f"   总职位数: {len(sample_jobs)} 条")
    print()
    
    # 转换为JobPosition对象
    jobs = []
    for job_data in sample_jobs:
        job = JobPosition(
            **job_data,
            source_website='demo',
            source_url='http://demo.com/search',
            job_detail_url='http://demo.com/job/123'
        )
        jobs.append(job)
    
    # 使用深度优化清洗
    print("🔍 开始深度优化清洗...")
    print()
    
    cleaned_jobs = cleaner.clean_job_list(
        [job.model_dump() for job in jobs],  # 使用model_dump替代dict
        'demo_website',
        'http://demo.com/search',
        'Python开发',
        max_days=30,
        require_completeness=True
    )
    
    print()
    print("📈 清洗结果分析:")
    print(f"   有效职位: {len(cleaned_jobs)} 条")
    print(f"   过滤率: {(len(sample_jobs) - len(cleaned_jobs)) / len(sample_jobs) * 100:.1f}%")
    
    # 显示匹配统计
    stats = cleaner.get_match_statistics()
    if stats:
        print()
        print("🎯 匹配质量统计:")
        print(f"   相关性匹配率: {stats['relevance_rate']:.1%}")
        print(f"   平均相关性得分: {stats['average_score']:.1f}")
        
        score_dist = stats.get('score_distribution', {})
        print(f"   高相关性(≥10分): {score_dist.get('high_relevance', 0)} 条")
        print(f"   中相关性(5-10分): {score_dist.get('medium_relevance', 0)} 条")
        print(f"   低相关性(2-5分): {score_dist.get('low_relevance', 0)} 条")
    
    print()
    print("✅ 保留的有效职位:")
    for i, job in enumerate(cleaned_jobs, 1):
        print(f"   {i}. {job.title} - {job.company}")
        print(f"      薪资: {job.salary} | 地点: {job.location}")
    
    print()
    print("🚫 被过滤的职位原因分析:")
    if hasattr(cleaner, 'match_details'):
        for job_id, detail in cleaner.match_details.items():
            if not detail['is_relevant']:
                print(f"   • {detail['title']} - {detail['company']}")
                print(f"     原因: {detail['explanation']}")
    
    print()
    print("🎉 深度优化演示完成！")
    
    return len(cleaned_jobs), len(sample_jobs)


def demo_time_parsing():
    """演示时间解析功能"""
    print("\n⏰ 时间解析功能演示")
    print("="*40)
    
    matcher = AdvancedKeywordMatcher()
    
    time_examples = [
        "今天发布",
        "昨天更新", 
        "3天前发布",
        "1周前",
        "2个月前",
        "2024-01-15",
        "无效时间格式"
    ]
    
    for time_text in time_examples:
        parsed_time = matcher.parse_publish_time(time_text)
        if parsed_time:
            days_ago = (datetime.now() - parsed_time).days
            print(f"   '{time_text}' -> {days_ago}天前")
        else:
            print(f"   '{time_text}' -> 解析失败")


def demo_completeness_check():
    """演示完整性检查功能"""
    print("\n📋 完整性检查功能演示")
    print("="*40)
    
    matcher = AdvancedKeywordMatcher()
    
    test_jobs = [
        {
            'name': '完整职位',
            'data': {
                'title': 'Python工程师',
                'company': '阿里巴巴',
                'location': '杭州',
                'salary': '25K-40K',
                'description': '详细的职位描述，包含技术要求和工作内容...' * 3,
                'requirements': '本科以上学历，3年经验',
                'education': '本科',
                'experience': '3年'
            }
        },
        {
            'name': '信息缺失职位',
            'data': {
                'title': 'Java工程师',
                'company': '',
                'location': '北京',
                'salary': '面议',
                'description': '简单描述',
                'requirements': '',
                'education': '',
                'experience': ''
            }
        }
    ]
    
    for test_job in test_jobs:
        is_complete, score, missing = matcher.check_job_completeness(test_job['data'])
        print(f"   {test_job['name']}:")
        print(f"     完整性得分: {score:.1f}/100")
        print(f"     是否完整: {'是' if is_complete else '否'}")
        if missing:
            print(f"     缺失字段: {missing}")


def main():
    """主演示函数"""
    try:
        # 运行主要演示
        valid_count, total_count = demo_advanced_matching()
        
        # 运行子功能演示
        demo_time_parsing()
        demo_completeness_check()
        
        print(f"\n📊 总结:")
        print(f"   原始职位: {total_count} 条")
        print(f"   有效职位: {valid_count} 条")
        print(f"   过滤效率: {(total_count - valid_count) / total_count * 100:.1f}%")
        print(f"   数据质量: 显著提升 ✨")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
