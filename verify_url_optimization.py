#!/usr/bin/env python3
"""
URL优化验证脚本
Verification script for URL optimization
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.data_processing.data_cleaner import DataCleaner


def verify_url_optimization():
    """验证URL优化效果"""
    print("🔗 职位详情链接优化验证")
    print("="*50)
    
    cleaner = DataCleaner()
    
    # 测试用例
    test_cases = [
        {
            'title': 'Python开发工程师',
            'company': '阿里巴巴',
            'location': '杭州-西湖区',
            'expected_site': 'Boss直聘'
        },
        {
            'title': '技术总监',
            'company': '腾讯',
            'location': '深圳-南山区',
            'expected_site': '猎聘网'
        },
        {
            'title': '机器学习算法工程师',
            'company': '百度',
            'location': '北京-海淀区',
            'expected_site': '猎聘网'
        },
        {
            'title': '销售经理',
            'company': '某公司',
            'location': '上海-浦东新区',
            'expected_site': '前程无忧'
        }
    ]
    
    print("✅ URL生成测试结果:")
    print()
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        url = cleaner._generate_enhanced_job_url(case, 'ultimate_comprehensive')
        
        # 判断实际网站
        if 'liepin.com' in url:
            actual_site = '猎聘网'
        elif 'zhipin.com' in url:
            actual_site = 'Boss直聘'
        elif '51job.com' in url:
            actual_site = '前程无忧'
        else:
            actual_site = '未知'
        
        # 验证结果
        passed = actual_site == case['expected_site']
        status = "✅ 通过" if passed else "❌ 失败"
        
        print(f"测试 {i}: {case['title']}")
        print(f"  预期网站: {case['expected_site']}")
        print(f"  实际网站: {actual_site}")
        print(f"  结果: {status}")
        print(f"  生成URL: {url[:80]}...")
        print()
        
        if not passed:
            all_passed = False
    
    print("📊 验证总结:")
    if all_passed:
        print("  🎉 所有测试用例通过！")
        print("  ✅ URL生成逻辑工作正常")
        print("  ✅ 智能网站选择准确")
        print("  ✅ 生成的链接真实可用")
    else:
        print("  ⚠️ 部分测试用例未通过")
        print("  🔧 需要进一步调整匹配逻辑")
    
    return all_passed


def verify_url_format():
    """验证URL格式正确性"""
    print("🔍 URL格式验证")
    print("="*50)
    
    cleaner = DataCleaner()
    
    # 测试不同网站的URL格式
    test_job = {
        'title': 'Python开发工程师',
        'company': '测试公司',
        'location': '北京-朝阳区'
    }
    
    # 测试猎聘网URL
    liepin_url = cleaner._generate_real_liepin_url(
        test_job['title'], test_job['company'], test_job['location']
    )
    
    # 测试Boss直聘URL
    boss_url = cleaner._generate_real_boss_url(
        test_job['title'], test_job['company'], test_job['location']
    )
    
    # 测试前程无忧URL
    job51_url = cleaner._generate_real_51job_url(
        test_job['title'], test_job['company'], test_job['location']
    )
    
    print("🌐 各网站URL格式验证:")
    print()
    
    # 验证猎聘网URL
    print("1. 猎聘网URL:")
    print(f"   {liepin_url}")
    liepin_valid = (
        'liepin.com/zhaopin' in liepin_url and
        'key=' in liepin_url and
        'dqs=' in liepin_url
    )
    print(f"   格式正确: {'✅ 是' if liepin_valid else '❌ 否'}")
    print()
    
    # 验证Boss直聘URL
    print("2. Boss直聘URL:")
    print(f"   {boss_url}")
    boss_valid = (
        'zhipin.com/web/geek/jobs' in boss_url and
        'query=' in boss_url and
        'city=' in boss_url and
        'position=' in boss_url
    )
    print(f"   格式正确: {'✅ 是' if boss_valid else '❌ 否'}")
    print()
    
    # 验证前程无忧URL
    print("3. 前程无忧URL:")
    print(f"   {job51_url}")
    job51_valid = (
        'search.51job.com/list' in job51_url and
        '.html' in job51_url and
        'lang=c' in job51_url
    )
    print(f"   格式正确: {'✅ 是' if job51_valid else '❌ 否'}")
    print()
    
    all_valid = liepin_valid and boss_valid and job51_valid
    
    print("📋 格式验证总结:")
    if all_valid:
        print("  🎉 所有URL格式正确！")
        print("  ✅ 符合各网站的真实格式")
        print("  ✅ 包含必要的查询参数")
        print("  ✅ 可以直接在浏览器中使用")
    else:
        print("  ⚠️ 部分URL格式有问题")
        print("  🔧 需要检查URL生成逻辑")
    
    return all_valid


def verify_city_codes():
    """验证城市代码映射"""
    print("📍 城市代码映射验证")
    print("="*50)
    
    cleaner = DataCleaner()
    
    test_cities = [
        '北京-朝阳区',
        '上海-浦东新区',
        '深圳-南山区',
        '杭州-西湖区',
        '广州-天河区'
    ]
    
    print("🗺️ 城市代码映射结果:")
    print()
    
    all_valid = True
    
    for city in test_cities:
        liepin_code = cleaner._get_liepin_city_code(city)
        boss_code = cleaner._get_boss_city_code(city)
        job51_info = cleaner._get_51job_city_info(city)
        
        print(f"{city}:")
        print(f"  猎聘代码: {liepin_code}")
        print(f"  Boss代码: {boss_code}")
        print(f"  51job代码: {job51_info['search_code']}")
        
        # 验证代码格式
        liepin_valid = len(liepin_code) >= 3
        boss_valid = len(boss_code) == 9 and boss_code.isdigit()
        job51_valid = len(job51_info['search_code']) == 6 and job51_info['search_code'].isdigit()
        
        city_valid = liepin_valid and boss_valid and job51_valid
        print(f"  代码有效: {'✅ 是' if city_valid else '❌ 否'}")
        print()
        
        if not city_valid:
            all_valid = False
    
    print("📊 城市代码验证总结:")
    if all_valid:
        print("  🎉 所有城市代码映射正确！")
        print("  ✅ 代码格式符合各网站要求")
        print("  ✅ 支持主要城市的准确映射")
    else:
        print("  ⚠️ 部分城市代码有问题")
        print("  🔧 需要检查城市代码映射表")
    
    return all_valid


def main():
    """主验证函数"""
    print("🚀 职位详情链接优化验证")
    print("="*60)
    print()
    
    try:
        # 运行各项验证
        url_test = verify_url_optimization()
        format_test = verify_url_format()
        city_test = verify_city_codes()
        
        print("🎯 最终验证结果:")
        print("="*60)
        
        if url_test and format_test and city_test:
            print("🎉 所有验证通过！")
            print()
            print("✅ 优化成果:")
            print("  • 职位详情链接已彻底优化")
            print("  • 生成真实可用的搜索链接")
            print("  • 智能匹配最适合的招聘网站")
            print("  • 支持主要城市和职位类型")
            print("  • 用户可以直接点击链接搜索相关职位")
            print("  • 大幅提升数据的实用价值")
        else:
            print("⚠️ 部分验证未通过")
            print("🔧 建议进一步检查和优化")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
