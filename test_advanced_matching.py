#!/usr/bin/env python3
"""
深度优化关键字匹配逻辑测试脚本
Test script for advanced keyword matching logic optimization
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.data_processing.data_cleaner import AdvancedKeywordMatcher, DataCleaner, JobPosition


def test_exclusion_keywords():
    """测试排除关键词功能"""
    print("🚫 测试排除关键词功能")
    print("="*50)
    
    matcher = AdvancedKeywordMatcher()
    
    # 测试用例：包含停招关键词的职位
    test_cases = [
        {
            'title': 'Python开发工程师（已停招）',
            'description': '负责后端开发工作',
            'expected': False,
            'reason': '包含停招标识'
        },
        {
            'title': 'Java工程师',
            'description': '职位已满，暂停招聘',
            'expected': False,
            'reason': '包含暂停招聘'
        },
        {
            'title': 'React前端开发',
            'description': 'HC已满，不再接收简历',
            'expected': False,
            'reason': '包含HC已满'
        },
        {
            'title': '算法工程师',
            'description': '负责机器学习算法开发，要求本科以上学历',
            'company': '百度',
            'location': '北京-海淀区',
            'salary': '30K-50K',
            'requirements': '本科以上学历，熟悉Python、TensorFlow',
            'expected': True,
            'reason': '正常有效职位'
        },
        {
            'title': '测试职位',
            'description': '这是一个测试用的职位',
            'expected': False,
            'reason': '测试职位'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        job_data = {
            'title': case['title'],
            'description': case['description'],
            'requirements': case.get('requirements', ''),
            'skills': '',
            'company': case.get('company', ''),
            'location': case.get('location', ''),
            'salary': case.get('salary', ''),
            'education': '',
            'experience': '',
            'benefits': ''
        }

        is_relevant, score, stats = matcher.is_relevant_job(job_data, "Python开发")
        
        print(f"测试 {i}: {case['title']}")
        print(f"  预期: {'相关' if case['expected'] else '不相关'} ({case['reason']})")
        print(f"  实际: {'相关' if is_relevant else '不相关'} (得分: {score:.1f})")
        
        if 'exclusion_reason' in stats:
            print(f"  排除原因: {stats['exclusion_reason']}")
        
        result = "✅ 通过" if (is_relevant == case['expected']) else "❌ 失败"
        print(f"  结果: {result}")
        print()


def test_time_validation():
    """测试时间验证功能"""
    print("⏰ 测试时间验证功能")
    print("="*50)
    
    matcher = AdvancedKeywordMatcher()
    
    # 测试时间解析
    time_test_cases = [
        ('今天', 0),
        ('昨天', 1),
        ('3天前', 3),
        ('2周前', 14),
        ('1个月前', 30),
        ('2024-01-15', None),  # 具体日期需要计算
        ('无效时间', None)
    ]
    
    print("时间解析测试:")
    for time_text, expected_days in time_test_cases:
        parsed_time = matcher.parse_publish_time(time_text)
        if parsed_time:
            days_diff = (datetime.now() - parsed_time).days
            print(f"  '{time_text}' -> {days_diff}天前 (预期: {expected_days})")
        else:
            print(f"  '{time_text}' -> 解析失败 (预期: {expected_days})")
    
    print()
    
    # 测试过期职位检测
    print("过期职位检测测试:")
    job_test_cases = [
        {
            'title': 'Python开发工程师',
            'description': '发布于今天，负责后端开发',
            'expected_expired': False
        },
        {
            'title': 'Java工程师',
            'description': '发布于35天前，负责系统开发',
            'expected_expired': True
        },
        {
            'title': 'React开发',
            'description': '负责前端开发工作',
            'publish_time': '15天前',
            'expected_expired': False
        }
    ]
    
    for i, case in enumerate(job_test_cases, 1):
        is_expired = matcher.is_job_expired(case, max_days=30)
        
        print(f"  测试 {i}: {case['title']}")
        print(f"    预期过期: {case['expected_expired']}")
        print(f"    实际过期: {is_expired}")
        result = "✅ 通过" if (is_expired == case['expected_expired']) else "❌ 失败"
        print(f"    结果: {result}")
    
    print()


def test_completeness_check():
    """测试完整性检查功能"""
    print("📋 测试完整性检查功能")
    print("="*50)
    
    matcher = AdvancedKeywordMatcher()
    
    test_cases = [
        {
            'name': '完整职位信息',
            'data': {
                'title': 'Python开发工程师',
                'company': '阿里巴巴',
                'location': '杭州-西湖区',
                'salary': '20K-35K',
                'description': '负责后端系统开发，参与架构设计，优化系统性能。要求熟悉Python、Django框架，有分布式系统经验。',
                'requirements': '本科以上学历，3年以上Python开发经验，熟悉MySQL、Redis等技术栈。',
                'education': '本科',
                'experience': '3-5年'
            },
            'expected_complete': True,
            'expected_score_range': (90, 100)
        },
        {
            'name': '基本信息缺失',
            'data': {
                'title': 'Java工程师',
                'company': '',
                'location': '北京',
                'salary': '面议',
                'description': '开发工作',
                'requirements': '',
                'education': '',
                'experience': ''
            },
            'expected_complete': False,
            'expected_score_range': (0, 50)
        },
        {
            'name': '中等完整度',
            'data': {
                'title': '前端开发',
                'company': '腾讯',
                'location': '深圳',
                'salary': '15K-25K',
                'description': '负责前端页面开发',
                'requirements': '',
                'education': '本科',
                'experience': '2年'
            },
            'expected_complete': True,
            'expected_score_range': (70, 90)
        }
    ]
    
    for case in test_cases:
        is_complete, score, missing_fields = matcher.check_job_completeness(case['data'])
        
        print(f"测试: {case['name']}")
        print(f"  预期完整: {case['expected_complete']}")
        print(f"  实际完整: {is_complete}")
        print(f"  完整性得分: {score:.1f}")
        print(f"  预期得分范围: {case['expected_score_range'][0]}-{case['expected_score_range'][1]}")
        
        if missing_fields:
            print(f"  缺失字段: {missing_fields}")
        
        score_in_range = case['expected_score_range'][0] <= score <= case['expected_score_range'][1]
        complete_match = is_complete == case['expected_complete']
        
        result = "✅ 通过" if (complete_match and score_in_range) else "❌ 失败"
        print(f"  结果: {result}")
        print()


def test_integrated_matching():
    """测试集成匹配功能"""
    print("🎯 测试集成匹配功能")
    print("="*50)
    
    cleaner = DataCleaner()
    
    # 创建测试职位
    test_jobs = [
        {
            'title': 'Python开发工程师',
            'company': '字节跳动',
            'location': '北京-朝阳区',
            'salary': '25K-40K',
            'description': '负责推荐系统后端开发，参与大数据处理和算法优化。要求熟悉Python、Django、MySQL、Redis等技术栈。',
            'requirements': '本科以上学历，3年以上Python开发经验，有大型互联网公司经验优先。',
            'education': '本科',
            'experience': '3-5年',
            'benefits': '五险一金，年终奖，股票期权，弹性工作'
        },
        {
            'title': 'Java工程师（已停招）',
            'company': '美团',
            'location': '上海',
            'salary': '20K-30K',
            'description': '负责后端开发工作',
            'requirements': '',
            'education': '',
            'experience': ''
        },
        {
            'title': '销售经理',
            'company': '某公司',
            'location': '广州',
            'salary': '8K-15K',
            'description': '负责产品销售工作',
            'requirements': '有销售经验',
            'education': '大专',
            'experience': '1年'
        }
    ]
    
    # 转换为JobPosition对象
    jobs = []
    for job_data in test_jobs:
        job = JobPosition(
            **job_data,
            source_website='test',
            source_url='http://test.com',
            job_detail_url='http://test.com/job/123'
        )
        jobs.append(job)
    
    print(f"原始职位数量: {len(jobs)}")
    
    # 使用深度优化清洗
    cleaned_jobs = cleaner.clean_job_list(
        [job.dict() for job in jobs],
        'test_website',
        'http://test.com/search',
        'Python开发',
        max_days=30,
        require_completeness=True
    )
    
    print(f"清洗后职位数量: {len(cleaned_jobs)}")
    
    # 显示匹配统计
    stats = cleaner.get_match_statistics()
    if stats:
        print(f"\n匹配统计:")
        print(f"  相关性匹配率: {stats['relevance_rate']:.1%}")
        print(f"  平均得分: {stats['average_score']:.1f}")
        
        score_dist = stats.get('score_distribution', {})
        print(f"  高相关性: {score_dist.get('high_relevance', 0)} 条")
        print(f"  中相关性: {score_dist.get('medium_relevance', 0)} 条")
        print(f"  低相关性: {score_dist.get('low_relevance', 0)} 条")


def main():
    """主测试函数"""
    print("🚀 深度优化关键字匹配逻辑测试")
    print("="*60)
    print()
    
    try:
        # 运行各项测试
        test_exclusion_keywords()
        test_time_validation()
        test_completeness_check()
        test_integrated_matching()
        
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
