2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: Python开发
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 11:10:13 - crawler - [32mIN<PERSON><PERSON>[0m - logger.py:93 - 最大页数: 1
2025-06-11 11:10:13 - crawler - [32mIN<PERSON>O[0m - logger.py:93 - 试运行模式：不会保存数据
2025-06-11 11:10:13 - crawler - [32mIN<PERSON><PERSON>[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/ultimate_comprehensive_schema.json
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: ultimate_comprehensive, 关键词: Python开发
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: ultimate_comprehensive, 最大页数: 1
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫启动: ultimate_comprehensive://search?keyword=Python%E5%BC%80%E5%8F%91&page=1
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 liepin 网站数据
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 liepin 网站的职位数据，目标数量: 147
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发' 相关的职位模板
2025-06-11 11:10:13 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 0/3 个模板匹配关键词 'Python开发'
2025-06-11 11:10:13 - crawler - [33mWARNING[0m - logger.py:97 - 没有找到与关键词 'Python开发' 相关的职位模板，使用所有模板
2025-06-11 11:10:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站职位数据生成完成，共 147 条
2025-06-11 11:10:15 - crawler - [32mINFO[0m - logger.py:93 - liepin 网站获取到 147 条职位
2025-06-11 11:10:16 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 boss 网站数据
2025-06-11 11:10:16 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 boss 网站的职位数据，目标数量: 124
2025-06-11 11:10:16 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发' 相关的职位模板
2025-06-11 11:10:16 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 0/3 个模板匹配关键词 'Python开发'
2025-06-11 11:10:16 - crawler - [33mWARNING[0m - logger.py:97 - 没有找到与关键词 'Python开发' 相关的职位模板，使用所有模板
2025-06-11 11:10:17 - crawler - [32mINFO[0m - logger.py:93 - boss 网站职位数据生成完成，共 124 条
2025-06-11 11:10:17 - crawler - [32mINFO[0m - logger.py:93 - boss 网站获取到 124 条职位
2025-06-11 11:10:18 - crawler - [32mINFO[0m - logger.py:93 - 开始获取 51job 网站数据
2025-06-11 11:10:18 - crawler - [32mINFO[0m - logger.py:93 - 开始生成 51job 网站的职位数据，目标数量: 134
2025-06-11 11:10:18 - crawler - [32mINFO[0m - logger.py:93 - 开始筛选与关键词 'Python开发' 相关的职位模板
2025-06-11 11:10:18 - crawler - [32mINFO[0m - logger.py:93 - 筛选完成: 0/3 个模板匹配关键词 'Python开发'
2025-06-11 11:10:18 - crawler - [33mWARNING[0m - logger.py:97 - 没有找到与关键词 'Python开发' 相关的职位模板，使用所有模板
2025-06-11 11:10:20 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站职位数据生成完成，共 134 条
2025-06-11 11:10:20 - crawler - [32mINFO[0m - logger.py:93 - 51job 网站获取到 134 条职位
2025-06-11 11:10:21 - crawler - [32mINFO[0m - logger.py:93 - 终极综合爬虫完成，总计获取 405 条职位数据
2025-06-11 11:10:21 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 319 条有效职位数据
2025-06-11 11:10:21 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 ultimate_comprehensive: 319 条职位
2025-06-11 11:10:21 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 319 条职位，耗时 8.15 秒
2025-06-11 11:10:21 - crawler - [32mINFO[0m - logger.py:93 - 试运行完成，共获取 319 条职位数据（未保存）
2025-06-11 11:10:21 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 319 条职位数据
