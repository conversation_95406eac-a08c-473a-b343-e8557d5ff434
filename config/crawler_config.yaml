# 工业职位需求爬虫配置文件
# Industrial Position Demand Crawler Configuration

# 爬虫基础设置
crawler:
  max_concurrent_requests: 3      # 最大并发请求数
  request_delay: 2.0             # 请求间隔(秒)
  timeout: 30                    # 请求超时时间(秒)
  retry_attempts: 3              # 重试次数
  headless: true                 # 是否使用无头浏览器

# 反反爬虫设置
anti_detection:
  rotate_user_agents: true       # 是否轮换用户代理
  use_proxy: false              # 是否使用代理
  proxy_list: []                # 代理列表
  random_delay_range: [1, 3]    # 随机延迟范围(秒)

# 数据存储设置
data_storage:
  output_format: "json"         # 输出格式: json, csv, database
  output_directory: "data/output"  # 输出目录
  database_url: "sqlite:///data/jobs.db"  # 数据库连接URL
  enable_deduplication: true    # 是否启用去重

# 网站配置
websites:
  # 终极综合爬虫 (三大网站全覆盖: 猎聘、Boss直聘、前程无忧)
  ultimate_comprehensive:
    base_url: "ultimate_comprehensive://"
    search_url_template: "ultimate_comprehensive://search?keyword={keyword}"
    enabled: true
    extraction_schema_path: "config/extraction_schemas/ultimate_comprehensive_schema.json"



# 爬取任务设置
crawl_settings:
  max_pages_per_site: 5         # 每个网站最大爬取页数
  enable_detail_crawl: false    # 是否爬取职位详情页
  concurrent_sites: 1          # 并发爬取的网站数量
  
# 数据过滤设置
data_filter:
  min_salary: 0                # 最低薪资要求(K)
  max_salary: 999              # 最高薪资要求(K)
  required_keywords:           # 必须包含的关键词
    - "计算机"
    - "软件"
    - "程序"
    - "开发"
    - "技术"
  excluded_keywords:           # 排除的关键词
    - "销售"
    - "客服"
    - "行政"

# 深度优化匹配设置
advanced_matching:
  # 时间验证设置
  time_validation:
    enabled: true              # 是否启用时间验证
    max_days: 30              # 最大允许天数（超过则视为过期）
    auto_parse_time: true     # 是否自动解析发布时间

  # 完整性检查设置
  completeness_check:
    enabled: true              # 是否启用完整性检查
    min_score: 70.0           # 最低完整性得分
    required_fields:          # 必填字段
      - "title"
      - "company"
      - "location"
      - "salary"
    important_fields:         # 重要字段
      - "description"
      - "requirements"

  # 关键词匹配设置
  keyword_matching:
    min_relevance_score: 2.0  # 最低相关性得分
    enable_semantic_match: true  # 启用语义匹配
    weight_adjustment: true   # 根据完整性调整权重

  # 排除增强设置
  exclusion_enhancement:
    strict_mode: true         # 严格模式（更严格的排除标准）
    check_hc_status: true     # 检查HC状态
    detect_test_jobs: true    # 检测测试职位
